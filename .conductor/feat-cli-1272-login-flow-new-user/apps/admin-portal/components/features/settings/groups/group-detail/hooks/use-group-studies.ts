import { skipToken, useQuery } from "@tanstack/react-query";

import api from "@/lib/apis";

export const USE_GROUP_STUDIES_QUERY_KEY = "group-studies";

export function useGroupStudies(groupId: string, take?: number) {
  const params = {
    take,
  };
  return useQuery({
    queryKey: [USE_GROUP_STUDIES_QUERY_KEY, groupId],
    queryFn: groupId ? () => api.groups.studies(groupId, params) : skipToken,
  });
}
