import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON><PERSON> } from "react-hook-form";
import { z } from "zod";

import { <PERSON><PERSON>, <PERSON>Button } from "@/components/ui/button";
import { Form, InputField } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { MultiSelect } from "@/components/ui/form/multi-select";
import { LazySelect } from "@/components/ui/lazy-select";
import { Modal } from "@/components/ui/modal";
import { useInfiniteGroups } from "@/hooks/queries/use-infinite-groups";
import { useInfiniteRoles } from "@/hooks/queries/use-infinite-roles";
import { RoleWithExtendType } from "@/lib/apis/roles";
import { capitalize } from "@/utils/string";

import { useGroupStudies } from "../../settings/groups/group-detail/hooks/use-group-studies";
import { useAddUser } from "../hooks/use-users-mutations";

const addUserSchema = z.object({
  firstName: z
    .string({ required_error: "First name is required" })
    .min(1, "First name is required"),
  lastName: z
    .string({ required_error: "Last name is required" })
    .min(1, "Last name is required"),
  email: z
    .string({ required_error: "Email is required" })
    .email("Invalid email format")
    .min(1, "Email is required"),
  phone: z.string().nullish(),
  roleId: z
    .string({ required_error: "Role is required" })
    .min(1, "Role is required"),
  groupId: z
    .string({ required_error: "Group is required" })
    .min(1, "Group is required"),
  studyIds: z.array(z.string()).min(1, "Study is required"),
});

type ModalAddUserProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const ModalAddUser = ({ isOpen, onClose }: ModalAddUserProps) => {
  const { mutateAsync: addUser, isPending } = useAddUser();

  async function onSubmit(data: z.infer<typeof addUserSchema>) {
    await addUser(data);
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Add User</Modal.Header>
      <Modal.Body>
        <Form
          mode="onBlur"
          schema={addUserSchema}
          onSubmit={onSubmit}
          defaultValues={{
            firstName: "",
            lastName: "",
            email: "",
            phone: "",
            roleId: "",
            groupId: "",
            studyIds: [],
          }}
          formProps={{ shouldFocusError: false }}
        >
          <UserForm onClose={onClose} isSubmitting={isPending} />
        </Form>
      </Modal.Body>
    </Modal>
  );
};

type UserFormProps = {
  onClose: () => void;
  isSubmitting?: boolean;
};

export const ADMIN_GROUP = "clincove";

const UserForm = ({ onClose, isSubmitting }: UserFormProps) => {
  const { field } = useController({
    name: "studyIds",
  });
  const [selectedRole, setSelectedRole] = useState<RoleWithExtendType | null>(
    null,
  );
  const [groupdId, setGroupId] = useState("");
  const { data } = useGroupStudies(groupdId, 1000);

  const options =
    data?.results?.map((study) => ({
      label: study.study.name,
      value: study.studyId,
    })) ?? [];

  useEffect(() => {
    const ids = data?.results.map((study) => study.studyId) ?? [];
    field.onChange(ids);
  }, [data]);

  return (
    <>
      <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-6">
        <div className="space-y-1">
          <Label htmlFor="firstName">First Name</Label>
          <InputField
            id="firstName"
            name="firstName"
            placeholder="Enter first name"
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="lastName">Last Name</Label>
          <InputField
            id="lastName"
            name="lastName"
            placeholder="Enter last name"
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="email">Email</Label>
          <InputField id="email" name="email" placeholder="Enter Email" />
        </div>

        <div className="space-y-1">
          <Label htmlFor="phone">Phone</Label>
          <InputField id="phone" name="phone" placeholder="Enter Phone" />
        </div>

        <div className="space-y-1">
          <Label htmlFor="roleId">Role</Label>
          <LazySelect
            name="roleId"
            id="roleId"
            searchPlaceholder="Search role..."
            useInfiniteQuery={useInfiniteRoles}
            getOptionLabel={(role) => `${role.name} (${capitalize(role.type)})`}
            getOptionValue={(role) => role.id}
            params={[]}
            onSelect={(role) => {
              setSelectedRole(role || null);
              setGroupId("");
            }}
            placeholder="Select role"
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="groupId">Group</Label>
          <LazySelect
            name="groupId"
            id="groupId"
            searchPlaceholder="Search groups..."
            useInfiniteQuery={useInfiniteGroups}
            getOptionLabel={(group) =>
              `${group.name} (${group.type === ADMIN_GROUP ? "Admin" : capitalize(group.type || "")})`
            }
            getOptionValue={(group) => group.id}
            onSelect={(group) => setGroupId(group?.id || "")}
            params={[
              selectedRole?.type === "admin" ? ADMIN_GROUP : selectedRole?.type,
            ]}
            dependentFieldNames={["roleId"]}
            placeholder="Select group"
          />
        </div>
        <div className="space-y-1 sm:col-span-2">
          <Label htmlFor="studyIds">Studies</Label>
          <MultiSelect
            name="studyIds"
            searchPlaceholder="Search study..."
            placeholder="Select study"
            searchable
            options={options}
          />
        </div>
      </div>
      <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5 ">
        <CloseButton onClose={onClose} />
        <Button type="submit" variant="primary" isLoading={isSubmitting}>
          Save
        </Button>
      </div>
    </>
  );
};
