"use client";

import { useSignIn } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useState } from "react";
import toast from "react-hot-toast";

import {
  useForgotPasswordRequest,
  useForgotPasswordResendCode,
  useForgotPasswordReset,
  useForgotPasswordVerifyCode,
} from "@/lib/apis/auth/hooks";
import { PASSWORD_RESET_COOLDOWN_SECONDS } from "@/lib/auth/constants";

import { SwitchCountries } from "../components/countries-selection";
import { GoBack } from "../components/go-back";
import { EmailStep } from "./email-step";
import { useResendCooldown } from "./hooks/use-resend-cooldown";
import { ResetPasswordStep } from "./reset-password-step";
import { SuccessStep } from "./success-step";
import type { EmailFormData, FormStep, PasswordResetFormData } from "./types";
import { handleApiError, handleOTPVerificationError } from "./utils";
import { VerificationStep } from "./verification-step";

/**
 * PasswordResetForm component orchestrates the four-step password reset flow:
 * 1. Email submission
 * 2. OTP verification
 * 3. New password creation
 * 4. Success message and session activation
 */
export function PasswordResetForm() {
  const { isLoaded, signIn, setActive } = useSignIn();
  const router = useRouter();

  // State management
  const [currentStep, setCurrentStep] = useState<FormStep>("email");
  const [email, setEmail] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [otpError, setOtpError] = useState<string | null>(null);
  const [createdSessionId, setCreatedSessionId] = useState<string | null>(null);
  const [protectionCode, setProtectionCode] = useState<string | null>(null);

  // Resend cooldown functionality
  const { cooldown: resendCooldown, startCooldown } = useResendCooldown();

  // React Query mutations
  const forgotPasswordRequest = useForgotPasswordRequest();
  const forgotPasswordVerifyCode = useForgotPasswordVerifyCode();
  const forgotPasswordReset = useForgotPasswordReset();
  const forgotPasswordResendCode = useForgotPasswordResendCode();

  // Calculate loading state from mutations
  const isLoading =
    forgotPasswordRequest.isPending ||
    forgotPasswordVerifyCode.isPending ||
    forgotPasswordReset.isPending ||
    forgotPasswordResendCode.isPending;

  // Step 1: Email submission handler
  const handleEmailSubmit = async (data: EmailFormData, formHandler: any) => {
    if (!isLoaded) return;

    setEmail(data.email);

    try {
      await forgotPasswordRequest.mutateAsync({ email: data.email });
      setCurrentStep("verification");
    } catch (error: any) {
      handleApiError(error, formHandler, "email");
    }
  };

  // Step 2: OTP verification handler
  const handleOTPSubmit = async () => {
    if (!isLoaded || verificationCode.length !== 6) return;

    try {
      const result = await forgotPasswordVerifyCode.mutateAsync({
        code: verificationCode,
        email,
      });

      if (result.isValid) {
        setProtectionCode(result.protectionCode);
        setCurrentStep("reset");
      } else {
        setOtpError(result.message || "Invalid verification code");
      }
    } catch (error: any) {
      const errorMessage = handleOTPVerificationError(error);
      if (errorMessage) {
        setOtpError(errorMessage);
      }
    }
  };

  // Step 3: Password reset handler
  const handlePasswordResetSubmit = async (
    data: PasswordResetFormData,
    formHandler: any,
  ) => {
    if (!isLoaded || !protectionCode) return;

    try {
      await forgotPasswordReset.mutateAsync({
        email,
        newPassword: data.password,
        protectionCode,
        signOutOfOtherSessions: data.signOutOfOtherSessions,
      });

      // After successful password reset, attempt to sign in the user
      try {
        const signInResult = await signIn.create({
          identifier: email,
          password: data.password,
        });

        if (signInResult.status === "complete") {
          setCreatedSessionId(signInResult.createdSessionId);
          setCurrentStep("success");
        }
      } catch {
        // If auto sign-in fails, still proceed to success step
        // User can manually sign in with new password
        setCurrentStep("success");
      }
    } catch (error: any) {
      handleApiError(error, formHandler, "reset");
    }
  };

  // Session activation handler for success step
  const handleSessionActivation = async () => {
    if (!isLoaded || !createdSessionId) return;

    try {
      await setActive({
        session: createdSessionId,
        redirectUrl: "/",
      });
    } catch (error: any) {
      console.error("Session activation error:", error);
      toast.error("Failed to sign in. Please try signing in manually.");
      // Redirect to sign-in page as fallback
      router.replace("/authentication/sign-in");
    }
  };

  // Resend verification code handler
  const handleResendCode = async () => {
    if (!isLoaded || resendCooldown > 0) return;

    try {
      await forgotPasswordResendCode.mutateAsync({ email });
      startCooldown(PASSWORD_RESET_COOLDOWN_SECONDS); // 60 second cooldown
    } catch (error: any) {
      console.error("Resend code error:", error);
      toast.error("Failed to resend code. Please try again.");
    }
  };

  const handleGoBack = () => {
    if (currentStep === "email") {
      // Navigate back to sign-in page
      router.replace("/authentication/sign-in");
    } else if (currentStep === "success") {
      // Don't allow going back from success step
      return;
    } else {
      // Reset form to initial state
      setCurrentStep("email");
      setEmail("");
      setVerificationCode("");
      setOtpError(null);
      setCreatedSessionId(null);
      setProtectionCode(null);
    }
  };

  const renderForm = () => {
    switch (currentStep) {
      case "email":
        return (
          <EmailStep
            isLoading={isLoading}
            isLoaded={isLoaded}
            email={email}
            onSubmit={handleEmailSubmit}
          />
        );

      case "verification":
        return (
          <VerificationStep
            isLoading={isLoading}
            isLoaded={isLoaded}
            email={email}
            verificationCode={verificationCode}
            setVerificationCode={setVerificationCode}
            onSubmit={handleOTPSubmit}
            onResendCode={handleResendCode}
            resendCooldown={resendCooldown}
            otpError={otpError}
            setOtpError={setOtpError}
          />
        );

      case "reset":
        return (
          <ResetPasswordStep
            isLoading={isLoading}
            isLoaded={isLoaded}
            email={email}
            onSubmit={handlePasswordResetSubmit}
          />
        );

      case "success":
        return (
          <SuccessStep
            isLoading={isLoading}
            isLoaded={isLoaded}
            email={email}
            onActivateSession={handleSessionActivation}
          />
        );

      default:
        return null;
    }
  };

  return (
    <>
      <div className="flex w-full items-center justify-between">
        {currentStep !== "success" ? (
          <GoBack onGoBack={handleGoBack} />
        ) : (
          <div></div>
        )}

        <SwitchCountries />
      </div>

      {renderForm()}
    </>
  );
}
