"use client";

import { <PERSON>, <PERSON>Off } from "lucide-react";
import Image from "next/image";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox, Form, InputField, Label } from "@/components/ui/form";
import { cn } from "@/lib/utils";

import { usePasswordVisibility } from "./hooks/use-password-visibility";
import { passwordResetSchema, type ResetPasswordStepProps } from "./types";

/**
 * ResetPasswordStep component for password reset flow
 * Handles new password input and confirmation with visibility toggles
 */
export function ResetPasswordStep({
  isLoading,
  isLoaded,
  onSubmit,
}: ResetPasswordStepProps) {
  const {
    showPassword,
    showConfirmPassword,
    togglePasswordVisibility,
    toggleConfirmPasswordVisibility,
  } = usePasswordVisibility();

  return (
    <div className="flex w-1/2 flex-col gap-5">
      <div className="flex flex-col items-center justify-center space-y-8 text-center">
        <Image
          src="/images/authentication/light-logo.svg"
          alt="logo"
          width={346}
          height={40}
        />
        <div className="space-y-2">
          <h1 className="min-h-16 text-[32px] font-semibold leading-[150%]">
            Reset Password
          </h1>
          <p className="text-gray-600">
            Create a new password for your account.
          </p>
        </div>
      </div>

      <Form
        schema={passwordResetSchema}
        onSubmit={onSubmit}
        defaultValues={{
          password: "",
          confirmPassword: "",
          signOutOfOtherSessions: false,
        }}
        isSubmitting={isLoading}
        mode="onChange"
        formProps={{ shouldFocusError: false }}
        className="flex flex-col gap-5"
      >
        <div>
          <Label htmlFor="password" required>
            New Password
          </Label>
          <div className="relative">
            <InputField
              id="password"
              name="password"
              type={showPassword ? "text" : "password"}
              autoComplete="new-password"
              placeholder="Enter your new password"
              className="pr-10"
            />
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className={cn(
                "absolute right-0 top-3 flex items-center pr-3",
                "text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100",
                "disabled:cursor-not-allowed disabled:opacity-50",
              )}
              aria-label={showPassword ? "Hide password" : "Show password"}
            >
              {showPassword ? (
                <EyeOff className="h-5 w-5 scale-x-[-1]" aria-hidden="true" />
              ) : (
                <Eye className="h-5 w-5" aria-hidden="true" />
              )}
            </button>
          </div>
        </div>

        <div>
          <Label htmlFor="confirmPassword" required>
            Confirm Password
          </Label>
          <div className="relative">
            <InputField
              id="confirmPassword"
              name="confirmPassword"
              type={showConfirmPassword ? "text" : "password"}
              autoComplete="new-password"
              placeholder="Confirm your new password"
              className="pr-10"
            />
            <button
              type="button"
              onClick={toggleConfirmPasswordVisibility}
              className={cn(
                "absolute right-0 top-3 flex items-center pr-3",
                "text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100",
                "disabled:cursor-not-allowed disabled:opacity-50",
              )}
              aria-label={
                showConfirmPassword ? "Hide password" : "Show password"
              }
            >
              {showConfirmPassword ? (
                <EyeOff className="h-5 w-5 scale-x-[-1]" aria-hidden="true" />
              ) : (
                <Eye className="h-5 w-5" aria-hidden="true" />
              )}
            </button>
          </div>
        </div>

        <div className="flex items-center gap-2 pb-5">
          <Checkbox id="signOutOfOtherSessions" name="signOutOfOtherSessions" />
          <Label
            htmlFor="signOutOfOtherSessions"
            className="text-sm font-medium leading-5"
          >
            Log out of all devices
          </Label>
        </div>

        <Button
          type="submit"
          isLoading={isLoading}
          disabled={!isLoaded}
          disabledForInvalid
          className="w-full"
        >
          {isLoading ? "Updating..." : "Update Password"}
        </Button>
      </Form>
    </div>
  );
}
