import type { UseFormReturn } from "react-hook-form";
import toast from "react-hot-toast";

/**
 * Handles API authentication errors and provides appropriate user feedback
 * @param error - The API error object
 * @param formHandler - Optional form handler for setting field-specific errors
 * @param context - Context for error handling ('email', 'verification', 'reset')
 */
export function handleApiError(
  error: any,
  formHandler?: UseFormReturn<any>,
  context: "email" | "verification" | "reset" = "email",
): void {
  console.error(`${context} error:`, error);

  if (error.message) {
    const apiError = error.message;

    switch (context) {
      case "email":
        handleEmailErrors(apiError, formHandler);
        break;
      case "verification":
        handleVerificationErrors(apiError);
        break;
      case "reset":
        handlePasswordResetErrors(apiError, formHandler);
        break;
      default:
        toast.error("Something went wrong. Please try again.");
    }
  } else {
    toast.error(error.message || "Something went wrong. Please try again.");
  }
}

/**
 * Handles Clerk authentication errors and provides appropriate user feedback
 * @param error - The Clerk error object
 * @param formHandler - Optional form handler for setting field-specific errors
 * @param context - Context for error handling ('email', 'verification', 'reset')
 */
export function handleClerkError(
  error: any,
  formHandler?: UseFormReturn<any>,
  context: "email" | "verification" | "reset" = "email",
): void {
  console.error(`${context} error:`, error);

  if (error.message && error.message.length > 0) {
    const clerkError = error.message;

    switch (context) {
      case "email":
        handleEmailErrors(clerkError, formHandler);
        break;
      case "verification":
        handleVerificationErrors(clerkError);
        break;
      case "reset":
        handlePasswordResetErrors(clerkError, formHandler);
        break;
      default:
        toast.error("Something went wrong. Please try again.");
    }
  } else {
    toast.error("Something went wrong. Please try again.");
  }
}

/**
 * Handle email submission specific errors
 */
function handleEmailErrors(error: any, formHandler?: UseFormReturn<any>): void {
  // Default error handling
  toast.error(
    error.longMessage ||
      error.message ||
      error ||
      "Failed to send reset code. Please try again.",
  );
}

/**
 * Handle OTP verification specific errors
 */
function handleVerificationErrors(error: any): string | null {
  toast.error(
    error.longMessage ||
      error.message ||
      error ||
      "Verification failed. Please try again.",
  );
  return null;
}

/**
 * Handle password reset specific errors
 */
function handlePasswordResetErrors(
  error: any,
  formHandler?: UseFormReturn<any>,
): void {
  toast.error(
    error.longMessage ||
      error.message ||
      error ||
      "Failed to reset password. Please try again.",
  );
}

/**
 * Special handler for OTP verification errors that need to set state
 * Returns the error message to be set in the component state
 */
export function handleOTPVerificationError(error: any): string | null {
  console.error("OTP verification error:", error);

  if (error.message && error.message > 0) {
    const apiError = error.message;
    return handleVerificationErrors(apiError);
  }

  toast.error("Something went wrong. Please try again.");
  return null;
}
