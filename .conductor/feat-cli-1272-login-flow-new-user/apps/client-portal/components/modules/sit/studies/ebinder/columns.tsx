"use client";

import { useDraggable } from "@dnd-kit/core";
import { ColumnDef } from "@tanstack/react-table";
import {
  FileClock,
  GripVertical,
  Pencil,
  Replace,
  Star,
  Trash2,
  Upload,
  View,
} from "lucide-react";
import { useId } from "react";
import { PiArrowBendRightDown } from "react-icons/pi";

import { PermissionGate } from "@/components/shared/permission/permission-gate";
import { RoleGate } from "@/components/shared/role";
import { DocumentTypeBadge } from "@/components/ui/badge/document-type-badge";
import {
  EbinderDocumentStatus,
  EbinderDocumentStatusBadge,
} from "@/components/ui/badge/ebinder-document-badge";
import { Tooltip } from "@/components/ui/tooltip";
import { useRole } from "@/hooks/auth";
import { usePermissions } from "@/hooks/auth/use-permissions";
import { EbinderDocument, EBinderFolder } from "@/lib/apis/isf-folders";
import { cn, formatDate } from "@/lib/utils";

import { getDocumentIcon } from "../../doc-exchange/icons";

type GenerateColumnsProps = {
  isShowDetail?: boolean;
  onPreview: (document: EbinderDocument) => void;
  onEdit: (document: EbinderDocument) => void;
  onViewAuditLog: (document: EbinderDocument) => void;
  onArchive: (document: EbinderDocument) => void;
  onUploadPlaceholder: (document: EbinderDocument) => void;
  onTriggerAction: (action: string) => void;
  onIndexing: (folder: EBinderFolder) => void;
  // Favorites functionality
  onToggleFavorite: (document: EbinderDocument) => void;
  isDocumentFavorited: (documentId: string) => boolean;
  favoritesLoading: boolean;
  isMyFavorites?: boolean;
};

const ActionButtons = ({
  document,
  onPreview,
  onEdit,
  onArchive,
  onViewAuditLog,
  onTriggerAction,
  onToggleFavorite,
  isDocumentFavorited,
  favoritesLoading,
}: Omit<
  GenerateColumnsProps,
  "onUploadPlaceholder" | "isShowDetail" | "onIndexing"
> & {
  document: EbinderDocument;
}) => {
  const isFavorited = isDocumentFavorited(document.id);
  const isProcessingFavorite = favoritesLoading;

  const { is } = useRole();
  const isCro = is("cro");

  return (
    <div className="flex items-center gap-2 whitespace-nowrap">
      {/* Favorite action - positioned first */}
      {!isCro && (
        <Tooltip
          content={isFavorited ? "Remove from favorites" : "Add to favorites"}
        >
          <Star
            size={20}
            onClick={() => {
              if (!isProcessingFavorite) {
                onTriggerAction(
                  isFavorited ? "remove-favorite" : "add-favorite",
                );
                onToggleFavorite(document);
              }
            }}
            className={cn(
              "cursor-pointer",
              isFavorited && "fill-yellow-300 text-yellow-300",
              !isFavorited && "text-gray-500",
            )}
          />
        </Tooltip>
      )}

      <Tooltip content="Preview">
        <View
          size={20}
          onClick={() => {
            onTriggerAction("preview");
            onPreview(document);
          }}
          className="cursor-pointer text-gray-500"
        />
      </Tooltip>
      <PermissionGate action="update:own" subject="isf">
        <RoleGate type="site">
          <Tooltip content="Edit">
            <Pencil
              size={20}
              onClick={() => {
                onTriggerAction("edit");
                onEdit(document);
              }}
              className="cursor-pointer text-gray-500"
            />
          </Tooltip>
        </RoleGate>
      </PermissionGate>
      <PermissionGate action="read:own" subject="isf">
        <Tooltip content="Audit log">
          <FileClock
            size={20}
            onClick={() => {
              onTriggerAction("audit-log");
              onViewAuditLog(document);
            }}
            className="cursor-pointer text-gray-500"
          />
        </Tooltip>
      </PermissionGate>
      <PermissionGate action="delete:own" subject="isf">
        <Tooltip content="Delete">
          <Trash2
            size={20}
            onClick={() => {
              onTriggerAction("confirm-delete");
              onArchive(document);
            }}
            className="cursor-pointer text-gray-500"
          />
        </Tooltip>
      </PermissionGate>
    </div>
  );
};

export const useEBinderColumns = ({
  onEdit,
  onPreview,
  onArchive,
  onViewAuditLog,
  onUploadPlaceholder,
  onTriggerAction,
  onIndexing,
  isShowDetail = false,
  onToggleFavorite,
  isDocumentFavorited,
  favoritesLoading,
  isMyFavorites = false,
}: GenerateColumnsProps) => {
  const dragHandleColumn: ColumnDef<EbinderDocument>[] = [
    {
      id: "drag-handle",
      header: "",
      cell: function DragHandle({ row }) {
        const { can } = usePermissions();
        const canUpdateDocuments = can("update:own", "isf");

        const { is } = useRole();
        const isCro = is("cro");

        const id = useId();
        const { attributes, listeners, setNodeRef, isDragging } = useDraggable({
          id: `${row.original.id}-${id}`,
          data: {
            document: row.original,
            type: "document",
          },
          disabled: !canUpdateDocuments,
        });

        if (!canUpdateDocuments || isCro) return null;

        return (
          <div
            ref={setNodeRef}
            {...attributes}
            {...listeners}
            className="flex cursor-grab items-center justify-center"
            style={{ opacity: isDragging ? 0.5 : 1 }}
          >
            <GripVertical size={20} className="text-gray-500" />
          </div>
        );
      },
      enableSorting: false,
      meta: { width: "40px" },
    },
  ];

  const baseColumns: ColumnDef<EbinderDocument>[] = [
    {
      header: "Type",
      accessorKey: "extension",
      cell: ({ row }) => {
        const extension = row.original.currentVersion?.fileRecord.extension;
        const status = row.original.isfStatus.name;

        if (status === "placeholder") {
          return <DocumentTypeBadge type="default">PH</DocumentTypeBadge>;
        }

        if (!extension)
          return <span className="whitespace-nowrap text-gray-400">N/A</span>;

        return (
          <div className="whitespace-nowrap">
            <DocumentTypeBadge type={extension} />
          </div>
        );
      },
    },
  ];

  const detailColumns: ColumnDef<EbinderDocument>[] = [
    {
      header: "Document ID",
      accessorKey: "id",
      cell: ({ row }) => {
        return (
          <span className="whitespace-nowrap">
            {row.original.id || <span className="text-gray-400">N/A</span>}
          </span>
        );
      },
    },
  ];

  const nameColumn: ColumnDef<EbinderDocument>[] = [
    {
      header: "Name",
      accessorKey: "title",
      cell: ({ row }) => {
        const icon = getDocumentIcon(
          row.original.currentVersion?.fileRecord.extension || "",
        );
        const status = row.original.isfStatus.name;

        if (status === "placeholder") {
          return (
            <div className="flex min-w-fit justify-between gap-2.5">
              <div
                className="flex min-w-fit cursor-pointer items-center gap-2 whitespace-nowrap"
                onClick={() => {
                  onTriggerAction("preview");
                  onPreview(row.original);
                }}
              >
                <Replace size={20} className="text-gray-500" />
                <div className="mr-4 whitespace-nowrap text-gray-400">
                  {row.original.title}
                </div>
                <PermissionGate action="create:own" subject="isf">
                  <RoleGate type="site">
                    <Upload
                      size={20}
                      className="cursor-pointer text-purple-500"
                      onClick={(e) => {
                        e.stopPropagation();
                        onUploadPlaceholder(row.original);
                      }}
                    />
                  </RoleGate>
                </PermissionGate>
              </div>
              {!isMyFavorites && (
                <Tooltip content={row.original.isfFolder?.name}>
                  <PiArrowBendRightDown
                    className="min-w-fit cursor-pointer"
                    onClick={() => {
                      if (!row.original.isfFolder) return;
                      onIndexing(row.original.isfFolder);
                    }}
                  />
                </Tooltip>
              )}
            </div>
          );
        }

        return (
          <div className="flex justify-between gap-2.5">
            <div
              className="flex flex-1 cursor-pointer items-center gap-2"
              onClick={() => {
                onTriggerAction("preview");
                onPreview(row.original);
              }}
            >
              {icon}
              <div className="mr-4 max-w-full flex-1 whitespace-nowrap">
                {row.original.title}
              </div>
            </div>
            {!isMyFavorites && (
              <Tooltip content={row.original.isfFolder?.name}>
                <PiArrowBendRightDown
                  className="min-w-fit cursor-pointer"
                  onClick={() => {
                    if (!row.original.isfFolder) return;
                    onIndexing(row.original.isfFolder);
                  }}
                />
              </Tooltip>
            )}
          </div>
        );
      },
    },
  ];

  const artifactColumn: ColumnDef<EbinderDocument>[] = [
    {
      header: "Artifact #",
      accessorKey: "artifactNumber",
      cell: ({ row }) => {
        return (
          <span className="whitespace-nowrap">
            {row.original.category?.isfRecordGroupName || (
              <span className="text-gray-400">N/A</span>
            )}
          </span>
        );
      },
    },
  ];

  const statusColumn: ColumnDef<EbinderDocument>[] = [
    {
      header: "Status",
      accessorKey: "status",
      cell: ({ row }) => {
        const status = row.original.isfStatus.name as EbinderDocumentStatus;
        return (
          <div className="whitespace-nowrap">
            <EbinderDocumentStatusBadge variant={status} />
          </div>
        );
      },
    },
  ];

  const expirationColumn: ColumnDef<EbinderDocument>[] = [
    {
      header: "Expiration",
      accessorKey: "expiryDate",
      cell: ({ row }) => {
        return row.original.expiryDate ? (
          <span className="whitespace-nowrap">
            {formatDate(row.original.expiryDate)}
          </span>
        ) : (
          <span className="whitespace-nowrap text-gray-400">N/A</span>
        );
      },
    },
  ];

  const remainingColumns: ColumnDef<EbinderDocument>[] = [
    {
      header: "Created Date",
      accessorKey: "createdDate",
      cell: ({ row }) => (
        <span className="whitespace-nowrap">
          {formatDate(row.original.createdDate)}
        </span>
      ),
    },
    {
      header: "Actions",
      accessorKey: "actions",
      enableSorting: false,
      cell: ({ row }: { row: { original: EbinderDocument } }) => (
        <ActionButtons
          document={row.original}
          onPreview={onPreview}
          onEdit={onEdit}
          onArchive={onArchive}
          onViewAuditLog={onViewAuditLog}
          onTriggerAction={onTriggerAction}
          onToggleFavorite={onToggleFavorite}
          isDocumentFavorited={isDocumentFavorited}
          favoritesLoading={favoritesLoading}
        />
      ),
    },
  ];

  const columns = [
    ...dragHandleColumn,
    ...baseColumns,
    ...(isShowDetail ? detailColumns : []),
    ...nameColumn,
    ...artifactColumn,
    ...statusColumn,
    ...(isShowDetail ? expirationColumn : []),
    ...remainingColumns,
  ];

  return columns;
};
