import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { PermissionGate } from "@/components/shared/permission/permission-gate";
import { Button } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import type { EBinderFolder } from "@/lib/apis/isf-folders/types";

import { useUpdateFolder } from "../hooks/folders";

type UpdateFolderModalProps = {
  showUpdateFolderModal: boolean;
  setShowUpdateFolderModal: (show: boolean) => void;
  folder: EBinderFolder;
  studyId: string;
  siteId: string;
};

const formSchema = z.object({
  name: z.string().min(1, { message: "Folder name is required" }),
});

type FormValues = z.infer<typeof formSchema>;

export const UpdateFolderModal = ({
  showUpdateFolderModal,
  setShowUpdateFolderModal,
  folder,
  studyId,
  siteId,
}: UpdateFolderModalProps) => {
  const { mutateAsync: updateFolder, isPending } = useUpdateFolder();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: folder.name,
    },
  });

  const onSubmit = async (values: FormValues) => {
    try {
      await updateFolder({
        folderId: folder.id,
        name: values.name,
        studyId,
        siteId,
      });
      setShowUpdateFolderModal(false);
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <PermissionGate action="update:own" subject="isf">
      <Modal
        show={showUpdateFolderModal}
        onClose={() => setShowUpdateFolderModal(false)}
      >
        <Modal.Header>Rename</Modal.Header>
        <Modal.Body className="pt-0">
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-4">
              <input
                placeholder="Enter folder name"
                {...form.register("name")}
                className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
              />
              {form.formState.errors.name && (
                <p className="mt-1 text-sm text-red-500">
                  {form.formState.errors.name.message}
                </p>
              )}
            </div>

            <div className="flex flex-col justify-end gap-4 sm:flex-row">
              <Button
                variant="outline"
                onClick={() => setShowUpdateFolderModal(false)}
                type="button"
                disabledForInvalid={false}
                className="border-none"
              >
                Cancel
              </Button>
              <Button variant="outline" type="submit" isLoading={isPending}>
                OK
              </Button>
            </div>
          </form>
        </Modal.Body>
      </Modal>
    </PermissionGate>
  );
};
