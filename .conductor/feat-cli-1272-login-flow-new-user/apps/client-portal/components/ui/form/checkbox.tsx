import { Checkbox as FlowbiteCheckbox } from "flowbite-react";
import React, { forwardRef } from "react";
import { Controller, useFormContext } from "react-hook-form";
import type { SetRequired } from "type-fest";

import { cn } from "@/lib/utils";

export type CheckboxProps = SetRequired<
  React.ComponentPropsWithoutRef<typeof FlowbiteCheckbox>,
  "name"
> & {
  shouldShowError?: boolean;
};

export const Checkbox = forwardRef<HTMLInputElement, CheckboxProps>(
  ({ name, shouldShowError = true, className, ...props }, ref) => {
    const { control } = useFormContext();
    return (
      <Controller
        control={control}
        name={name}
        render={({ field, formState: { errors } }) => {
          const errorMessage = errors[name]?.message?.valueOf();
          const hasError = typeof errorMessage === "string";

          return (
            <div className="flex flex-col">
              <FlowbiteCheckbox
                {...props}
                {...field}
                ref={ref}
                className={cn(
                  className,
                  // Focus state - blue border and light blue background
                  "checked:!bg-purple-500 focus:!border-purple-500 focus:!ring-purple-500",
                  // Error state - red border and light red background
                  hasError &&
                    "[&_input:checked]:!bg-red-500 [&_input]:!border-red-500 [&_input]:!ring-red-500",
                )}
              />
              {hasError && shouldShowError && (
                <span className="mt-1 text-sm text-red-500">
                  {errorMessage}
                </span>
              )}
            </div>
          );
        }}
      />
    );
  },
);

Checkbox.displayName = "Checkbox";

const CheckboxField = ({ className, ...props }: CheckboxProps) => {
  return (
    <FlowbiteCheckbox
      {...props}
      className={cn(className, "focus:border-purple-500 focus:ring-purple-500")}
    />
  );
};

export { CheckboxField };
