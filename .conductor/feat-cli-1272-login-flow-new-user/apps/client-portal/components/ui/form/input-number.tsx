"use client";
import type { TextInputProps } from "flowbite-react";
import { Minus, Plus } from "lucide-react";
import { forwardRef } from "react";
import { Controller, useFormContext } from "react-hook-form";
import * as R from "remeda";
import type { Merge, SetRequired } from "type-fest";

import { cn } from "@/lib/utils";

import { Button } from "../button";

// Helper function to determine decimal places in a number
const getDecimalPlaces = (num: number): number => {
  if (Math.floor(num) === num) return 0;
  const str = num.toString();
  if (str.indexOf(".") !== -1 && str.indexOf("e-") === -1) {
    return str.split(".")[1].length;
  } else if (str.indexOf("e-") !== -1) {
    const parts = str.split("e-");
    return parseInt(parts[1], 10) + (parts[0].split(".")[1]?.length || 0);
  }
  return 0;
};

// Helper function to perform precise arithmetic
const preciseArithmetic = (
  value: number,
  step: number,
  operation: "add" | "subtract",
): number => {
  const decimalPlaces = Math.max(
    getDecimalPlaces(value),
    getDecimalPlaces(step),
  );
  const multiplier = Math.pow(10, decimalPlaces);

  const valueInt = Math.round(value * multiplier);
  const stepInt = Math.round(step * multiplier);

  let resultInt: number;
  if (operation === "add") {
    resultInt = valueInt + stepInt;
  } else {
    resultInt = valueInt - stepInt;
  }

  const result = resultInt / multiplier;

  // Round to the appropriate decimal places to avoid any residual precision issues
  return Number(result.toFixed(decimalPlaces));
};

export type InputNumberProps = Merge<
  SetRequired<TextInputProps, "name">,
  {
    shouldShowError?: boolean;
    min?: number;
    max?: number;
    step?: number;
    defaultValue?: number | null;
    showButtons?: boolean;
    allowNegative?: boolean;
  }
>;

const InputNumber = forwardRef<HTMLInputElement, InputNumberProps>(
  (
    {
      name,
      shouldShowError = true,
      min,
      max,
      step = 1,
      defaultValue = null,
      className,
      showButtons = true,
      allowNegative = true,
      type = "text",
      ...props
    },
    ref,
  ) => {
    const { control } = useFormContext();
    const isNumberType = type === "number";

    return (
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue === null ? null : defaultValue.toString()}
        render={({ field, formState: { errors } }) => {
          const errorMessage = R.pathOr(
            errors,
            [...name.split(".")] as any,
            {},
          )?.message?.valueOf();
          const hasError = typeof errorMessage === "string";

          const handleDecrement = () => {
            const currentValue =
              field.value === null || field.value === ""
                ? 0
                : parseFloat(field.value);
            const newValue = preciseArithmetic(currentValue, step, "subtract");
            if (min !== undefined && newValue < min) return;
            field.onChange(newValue.toString());
          };

          const handleIncrement = () => {
            const currentValue =
              field.value === null || field.value === ""
                ? 0
                : parseFloat(field.value);
            const newValue = preciseArithmetic(currentValue, step, "add");
            if (max !== undefined && newValue > max) return;
            field.onChange(newValue.toString());
          };

          const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            const value = e.target.value;
            // Allow empty string, negative sign, or numbers
            if (
              value === "" ||
              (allowNegative && value === "-") ||
              /^-?\d*\.?\d*$/.test(value)
            ) {
              // Convert empty string to null
              field.onChange(value === "" ? null : value);
            }
          };

          const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
            const value = e.target.value;

            // Only handle minus sign and numeric validation
            if (value === "-") {
              // Just minus sign is not valid, set to null
              field.onChange(null);
            } else if (value !== "" && value !== null) {
              // Only apply min/max constraints if there's a value
              const numValue = parseFloat(value);
              if (min !== undefined && numValue < min) {
                field.onChange(min.toString());
              } else if (max !== undefined && numValue > max) {
                field.onChange(max.toString());
              }
            }

            field.onBlur();
          };

          // Ensure value is properly formatted for display
          let inputValue = "";

          // Handle various field value types for display
          if (
            field.value !== null &&
            field.value !== undefined &&
            field.value !== ""
          ) {
            if (typeof field.value === "number") {
              inputValue = field.value.toString();
            } else if (typeof field.value === "string") {
              // Only use valid numeric strings
              if (field.value === "-" || /^-?\d*\.?\d*$/.test(field.value)) {
                inputValue = field.value;
              }
            }
          }

          const currentValueNum =
            field.value === null || field.value === ""
              ? 0
              : parseFloat(field.value);

          return (
            <>
              <div className="flex w-full items-center gap-[6px]">
                <Button
                  variant="outline"
                  className={cn("!size-11", !showButtons && "hidden")}
                  onClick={handleDecrement}
                  type="button"
                  disabled={
                    (min !== undefined && currentValueNum <= min) ||
                    props.disabled
                  }
                >
                  <Minus />
                </Button>
                <input
                  {...props}
                  type={type}
                  ref={ref}
                  max={max}
                  min={min}
                  name={field.name}
                  value={inputValue}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  placeholder="0"
                  className={cn(
                    " block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-center text-sm text-gray-900 focus:border-purple-500 focus:ring-purple-500",
                    "focus:!bg-purple-50/50",
                    hasError && "!border-red-500 !bg-red-50 !ring-red-500",
                    isNumberType && "number-no-arrows",
                    className,
                  )}
                />
                <Button
                  variant="outline"
                  onClick={handleIncrement}
                  type="button"
                  className={cn("!size-11", !showButtons && "hidden")}
                  disabled={
                    (max !== undefined && currentValueNum >= max) ||
                    props.disabled
                  }
                >
                  <Plus />
                </Button>
              </div>
              {hasError && shouldShowError && (
                <span className="error-message">{errorMessage}</span>
              )}
            </>
          );
        }}
      />
    );
  },
);

InputNumber.displayName = "InputNumber";

export { InputNumber };
