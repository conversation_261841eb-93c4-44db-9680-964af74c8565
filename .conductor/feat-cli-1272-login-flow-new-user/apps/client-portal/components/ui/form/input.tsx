"use client";
import type { TextInputProps } from "flowbite-react";
import { TextInput } from "flowbite-react";
import { CircleAlert } from "lucide-react";
import { forwardRef } from "react";
import { Controller, useFormContext } from "react-hook-form";
import type { Merge, SetRequired } from "type-fest";

import { cn } from "@/lib/utils";

export type InputFieldProps = Merge<
  SetRequired<TextInputProps, "name">,
  {
    shouldShowError?: boolean;
  }
>;

const InputField = forwardRef<HTMLInputElement, InputFieldProps>(
  ({ name, className, shouldShowError = true, ...props }, ref) => {
    const { control } = useFormContext();

    return (
      <Controller
        name={name}
        control={control}
        render={({ field, formState: { errors } }) => {
          const errorMessage = errors[name]?.message?.valueOf();
          const hasError = typeof errorMessage === "string";

          return (
            <>
              <TextInput
                {...field}
                {...props}
                ref={ref}
                onChange={(e) => {
                  field.onChange(e);
                  props.onChange?.(e);
                }}
                theme={{
                  field: {
                    input: {
                      base: cn(
                        "h-11 w-full",
                        "focus:!border-purple-400 focus:!bg-purple-50/50 focus:!ring-purple-500",
                        hasError &&
                          "!border-red-500 !bg-red-50 !ring-red-500 focus:!border-red-500 focus:!bg-red-50 focus:!ring-red-500",
                        className,
                      ),
                    },
                  },
                }}
              />
              {hasError && shouldShowError && (
                <div className="flex items-start gap-1">
                  <CircleAlert className="mt-1.5 h-3 w-3 text-red-500" />
                  <span className="error-message">{errorMessage}</span>
                </div>
              )}
            </>
          );
        }}
      />
    );
  },
);

InputField.displayName = "InputField";

export { InputField };
