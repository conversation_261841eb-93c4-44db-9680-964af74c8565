"use client";

import {
  autoUpdate,
  flip,
  FloatingFocusManager,
  FloatingPortal,
  offset,
  shift,
  size,
  useClick,
  useDismiss,
  useFloating,
  useInteractions,
} from "@floating-ui/react";
import type { SelectProps as FlowbiteSelectProps } from "flowbite-react";
import { ChevronDown } from "lucide-react";
import { forwardRef, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { MdOutlineClear } from "react-icons/md";

import { cn } from "@/lib/utils";

type Option = {
  label: string;
  value: string;
};

export type SelectProps<T extends Option> = Omit<
  FlowbiteSelectProps,
  "name" | "onChange"
> & {
  name: string;
  shouldShowError?: boolean;
  placeholder?: string;
  isDisabled?: boolean;
  options?: T[];
  onChange?: (value: string) => void;
  className?: string;
  renderOption?: (option: T) => React.ReactNode;
  renderSelectedOption?: (option: T) => React.ReactNode;
};

function SelectComponent<T extends Option = Option>(
  {
    name,
    shouldShowError = true,
    placeholder = "Select an option",
    isDisabled = false,
    options = [],
    onChange,
    className,
    renderOption,
    renderSelectedOption,
  }: SelectProps<T>,
  ref: React.ForwardedRef<HTMLDivElement>,
) {
  const { control } = useFormContext();
  const [isOpen, setIsOpen] = useState(false);

  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: setIsOpen,
    placement: "bottom-start",
    middleware: [
      offset(8),
      flip({
        fallbackPlacements: ["top-start"],
        fallbackStrategy: "bestFit",
        padding: 1,
        crossAxis: false,
      }),
      shift({
        padding: 1,
      }),
      size({
        apply({ rects, elements }) {
          Object.assign(elements.floating.style, {
            width: `${rects.reference.width}px`,
          });
        },
        padding: 1,
      }),
    ],
    whileElementsMounted: autoUpdate,
  });

  const { getReferenceProps, getFloatingProps } = useInteractions([
    useClick(context),
    useDismiss(context),
  ]);

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, formState: { errors } }) => {
        const errorMessage = errors[name]?.message?.valueOf();
        const hasError = typeof errorMessage === "string";

        const selectedOption = options.find(
          (option) => option.value === field.value,
        );

        return (
          <div
            className={cn(
              "relative",
              hasError && shouldShowError && "mb-5",
              className,
            )}
            ref={ref}
          >
            {/* Select Button */}
            <div
              ref={refs.setReference}
              {...getReferenceProps()}
              className="relative h-full w-full"
            >
              <button
                type="button"
                className={cn(
                  "group flex w-full items-center justify-between rounded-lg border bg-gray-50 px-[14px] py-2.5 text-left text-base leading-6 focus:outline-none focus:ring-1",
                  isDisabled
                    ? "cursor-not-allowed border-gray-200 bg-gray-50 text-gray-500"
                    : "border-gray-300 hover:bg-gray-50",
                  hasError
                    ? "border-red-500 bg-red-50 hover:!bg-red-50 focus:ring-red-500"
                    : "focus:border-purple-500 focus:bg-purple-50/50 focus:ring-purple-500",
                  "min-h-11 gap-2 transition-all duration-200 ease-in-out",
                )}
                disabled={isDisabled}
              >
                <span
                  className={cn("truncate", !selectedOption && "text-gray-500")}
                >
                  {selectedOption
                    ? renderSelectedOption
                      ? renderSelectedOption(selectedOption)
                      : selectedOption.label
                    : placeholder}
                </span>
                {selectedOption ? (
                  <div
                    role="button"
                    tabIndex={0}
                    className="cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      field.onChange("");
                      onChange?.("");
                    }}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" || e.key === " ") {
                        e.preventDefault();
                        e.stopPropagation();
                        field.onChange("");
                        onChange?.("");
                      }
                    }}
                  >
                    <MdOutlineClear className="size-4" />
                  </div>
                ) : (
                  <ChevronDown
                    className={cn(
                      "size-4 shrink-0 transition-transform duration-200",
                      "text-gray-400",
                      "group-focus:text-purple-500",
                      hasError && "!text-red-500",
                      isOpen ? "-rotate-180 transform" : "",
                    )}
                  />
                )}
              </button>

              {hasError && shouldShowError && (
                <span className="error-message absolute left-0 top-full">
                  {errorMessage}
                </span>
              )}
            </div>

            {/* Dropdown Panel */}
            {isOpen && (
              <FloatingPortal>
                <FloatingFocusManager context={context} modal={false}>
                  <div
                    ref={refs.setFloating}
                    style={floatingStyles}
                    {...getFloatingProps()}
                    className="z-50 rounded-lg border border-gray-200 bg-white py-1 shadow-lg"
                  >
                    <div className="max-h-60 overflow-y-auto">
                      {options.length === 0 ? (
                        <div className="flex h-10 items-center justify-center px-3 py-2 text-sm text-gray-500">
                          No options available
                        </div>
                      ) : (
                        options.map((option) => {
                          const isSelected = field.value === option.value;
                          return (
                            <button
                              key={option.value}
                              type="button"
                              className={cn(
                                "w-full px-3 py-2 text-left text-sm",
                                "hover:bg-gray-100",
                                isSelected
                                  ? "bg-primary-50 text-primary-600"
                                  : "text-gray-900",
                              )}
                              onClick={() => {
                                field.onChange(option.value);
                                onChange?.(option.value);
                                setIsOpen(false);
                              }}
                            >
                              <span className="block truncate">
                                {renderOption
                                  ? renderOption(option)
                                  : option.label}
                              </span>
                            </button>
                          );
                        })
                      )}
                    </div>
                  </div>
                </FloatingFocusManager>
              </FloatingPortal>
            )}
          </div>
        );
      }}
    />
  );
}

const Select = forwardRef(SelectComponent) as <T extends Option = Option>(
  props: SelectProps<T> & { ref?: React.ForwardedRef<HTMLDivElement> },
) => ReturnType<typeof SelectComponent>;

export { Select };
