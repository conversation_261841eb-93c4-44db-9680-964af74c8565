import type { TextareaProps as FlowbiteTextareaProps } from "flowbite-react";
import { Textarea as FlowbiteTextarea } from "flowbite-react";
import { forwardRef } from "react";
import { Controller, useFormContext } from "react-hook-form";
import type { Merge, SetRequired } from "type-fest";

import { cn } from "@/lib/utils";

export type TextareaProps = Merge<
  SetRequired<FlowbiteTextareaProps, "name">,
  {
    shouldShowError?: boolean;
  }
>;

const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ name, shouldShowError = true, ...props }, ref) => {
    const { control } = useFormContext();
    return (
      <Controller
        name={name}
        control={control}
        render={({ field, formState: { errors } }) => {
          const errorMessage = errors[name]?.message?.valueOf();
          const hasError = typeof errorMessage === "string";

          return (
            <>
              <FlowbiteTextarea
                {...field}
                {...props}
                ref={ref}
                className={cn(
                  // Focus state - blue border and light blue background
                  "px-[14px] py-2.5 [&]:focus:!border-purple-500 [&]:focus:!bg-purple-50/50 [&]:focus:!ring-purple-500",
                  // Error state - red border and light red background
                  hasError &&
                    "[&]:!border-red-500 [&]:!bg-red-50 [&]:!ring-red-500",
                  props.className,
                )}
              />
              {hasError && shouldShowError && (
                <span className="mt-1 text-sm text-red-500">
                  {errorMessage}
                </span>
              )}
            </>
          );
        }}
      />
    );
  },
);

Textarea.displayName = "Textarea";

export { Textarea };
