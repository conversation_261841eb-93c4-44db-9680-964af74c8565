"use client";

import type { SessionWithActivitiesResource } from "@clerk/types";
import { format, isToday, isYesterday } from "date-fns";
import { Laptop, TabletSmartphone } from "lucide-react";

import { cn } from "@/lib/utils";

import { Button } from "../ui/button";

interface ActiveDevicesSectionProps {
  sessions: SessionWithActivitiesResource[] | null | undefined;
  sessionsLoaded: boolean;
}

export const ActiveDevicesSection = ({
  sessions,
  sessionsLoaded,
}: ActiveDevicesSectionProps) => {
  const formatLastActive = (timestamp: string | number | Date) => {
    const date = new Date(timestamp);

    if (isToday(date)) {
      const timeString = format(date, "h:mm a");
      return `Today at ${timeString}`;
    } else if (isYesterday(date)) {
      const timeString = format(date, "h:mm a");
      return `Yesterday at ${timeString}`;
    } else {
      const formattedDate = format(date, "MMM dd, yyyy");
      const timeString = format(date, "h:mm a");
      return `${formattedDate} at ${timeString}`;
    }
  };

  const getCurrentSession = () => {
    if (!sessions) return null;
    return sessions.find((session) => session.status === "active");
  };

  const currentSession = getCurrentSession();

  return (
    <div className="flex gap-4 pt-8">
      <h3 className="font-poppins min-w-[150px] text-xs font-normal leading-6">
        Active Devices
      </h3>

      {!sessionsLoaded ? (
        <div className="p-4 text-center text-gray-500">
          Loading active sessions...
        </div>
      ) : sessions && sessions.length > 0 ? (
        <div className="flex-1 space-y-[30px]">
          {sessions.map((session) => {
            const isCurrentSession = session.id === currentSession?.id;

            const deviceName = session.latestActivity.deviceType;
            const isMobile = session.latestActivity.isMobile;

            const browserInfo = session.latestActivity.browserName;
            const browserVersion = session.latestActivity.browserVersion;
            const lastActive = formatLastActive(session.lastActiveAt);

            // IP Information
            const ipAddress = session.latestActivity.ipAddress;
            const city = session.latestActivity.city;
            const country = session.latestActivity.country;

            return (
              <div key={session.id} className="flex items-start gap-2.5">
                {isMobile ? (
                  <TabletSmartphone size={24} />
                ) : (
                  <Laptop size={24} />
                )}
                <div className="flex flex-1 items-start justify-between">
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900">
                        {deviceName}
                      </span>
                      {isCurrentSession && (
                        <span
                          className={cn(
                            "rounded-full bg-gray-100 px-2.5 py-1 text-xs text-gray-600",
                            "before:mr-1.5 before:inline-block before:h-1.5 before:w-1.5 before:rounded-full before:bg-gray-600",
                          )}
                        >
                          This device
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-600">
                      {browserInfo} {browserVersion}
                    </div>
                    <div className="text-sm text-gray-500">
                      {ipAddress} ({city} {country})
                    </div>
                    <div className="text-sm text-gray-500">{lastActive}</div>
                  </div>

                  {!isCurrentSession && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        session.revoke();
                      }}
                    >
                      Sign out
                    </Button>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="p-4 text-center text-gray-500">
          No active sessions found
        </div>
      )}
    </div>
  );
};
