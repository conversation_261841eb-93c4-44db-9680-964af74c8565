"use client";

import type { UserResource } from "@clerk/types";
import { useState } from "react";
import toast from "react-hot-toast";
import { z } from "zod";

import { But<PERSON> } from "../ui/button";
import { CheckboxField, Form, InputField } from "../ui/form";

const passwordFormSchema = z
  .object({
    currentPassword: z.string().min(1, "Current password is required"),
    newPassword: z.string().min(8, "Password must be at least 8 characters"),
    confirmPassword: z.string().min(1, "Please confirm your password"),
    signOutAllDevices: z.boolean().default(false),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Password do not match",
    path: ["confirmPassword"],
  });

type PasswordFormData = z.infer<typeof passwordFormSchema>;

interface PasswordSectionProps {
  user: UserResource | null | undefined;
}

export const PasswordSection = ({ user }: PasswordSectionProps) => {
  const [isUpdatingPassword, setIsUpdatingPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const defaultValues = {
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
    signOutAllDevices: false,
  };

  const handleUpdatePassword = () => {
    setIsUpdatingPassword(true);
  };

  const handleCancelPasswordUpdate = () => {
    setIsUpdatingPassword(false);
  };

  const onSubmit = async (data: PasswordFormData, formHandler: any) => {
    if (!user) return;

    setIsSubmitting(true);
    try {
      await user.updatePassword({
        currentPassword: data.currentPassword,
        newPassword: data.newPassword,
        signOutOfOtherSessions: data.signOutAllDevices,
      });

      // Success: reset form and hide update UI
      setIsUpdatingPassword(false);
      formHandler.reset();
      toast.success("Password updated successfully!");
    } catch (error: any) {
      if (error.errors) {
        const clerkError = error.errors[0];
        switch (clerkError.code) {
          case "form_password_incorrect":
          case "form_password_validation_failed":
            formHandler.setError("currentPassword", {
              message: "Incorrect password",
            });
            break;
          case "form_password_too_weak":
          case "password_not_strong_enough":
            formHandler.setError("newPassword", {
              message:
                clerkError.longMessage ||
                clerkError.message ||
                "Password is not strong enough",
            });
            break;
          case "form_password_pwned":
            formHandler.setError("newPassword", {
              message:
                "This password has been found in a data breach. Please choose a different password.",
            });
            break;
          case "form_password_size_in_bytes_exceeded":
            formHandler.setError("newPassword", {
              message: "Password is too long",
            });
            break;
          case "too_many_requests":
            toast.error("Too many attempts. Please try again later.");
            break;
          default:
            toast.error(
              clerkError.longMessage ||
                clerkError.message ||
                "Password update failed. Please try again.",
            );
        }
      } else {
        toast.error(
          "Something went wrong. Please check your connection and try again.",
        );
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="mb-5 flex gap-4">
      <h3 className="font-poppins min-w-[150px] text-xs font-normal leading-6">
        Password
      </h3>

      {!isUpdatingPassword ? (
        <div className="flex flex-1 items-center justify-between">
          <div className="flex items-center space-x-3">
            <span className="font-mono text-base font-normal leading-6">
              **********
            </span>
          </div>
          <Button
            onClick={handleUpdatePassword}
            className="text-base font-medium leading-5"
            aria-label="Update password"
            variant="outline"
          >
            Update password
          </Button>
        </div>
      ) : (
        <Form
          schema={passwordFormSchema}
          defaultValues={defaultValues}
          onSubmit={onSubmit}
          isSubmitting={isSubmitting}
          className="flex-1 space-y-4"
        >
          <div className="text-xl font-bold leading-[150%]">
            Update Password
          </div>
          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700">
              Current Password
            </label>
            <InputField
              name="currentPassword"
              type="password"
              placeholder="Enter your current password"
            />
          </div>

          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700">
              New Password
            </label>
            <InputField
              name="newPassword"
              type="password"
              placeholder="Enter your new password"
            />
          </div>

          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700">
              Confirm Password
            </label>
            <InputField
              name="confirmPassword"
              type="password"
              placeholder="Confirm your new password"
            />
          </div>

          <div className="pt-2">
            <div className="flex items-start space-x-2">
              <CheckboxField
                id="signOutAllDevices"
                name="signOutAllDevices"
                className="text-purple-600"
              />
              <label
                htmlFor="signOutAllDevices"
                className="flex select-none flex-col text-sm font-medium"
              >
                <span>Sign out of all other devices</span>
                <span className="text-gray-500">
                  It is recommended to sign out of all other devices which may
                  have used your old password
                </span>
              </label>
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancelPasswordUpdate}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" variant="primary" isLoading={isSubmitting}>
              Save
            </Button>
          </div>
        </Form>
      )}
    </div>
  );
};
