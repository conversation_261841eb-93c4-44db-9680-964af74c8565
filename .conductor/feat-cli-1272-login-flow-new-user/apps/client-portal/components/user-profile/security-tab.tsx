"use client";

import { useUser } from "@clerk/nextjs";
import { SessionWithActivitiesResource } from "@clerk/types";
import { useEffect, useState } from "react";

import { ActiveDevicesSection } from "./active-devices-section";
import { PasswordSection } from "./password-section";

export const SecurityTab = () => {
  const { user } = useUser();
  const [sessions, setSessions] = useState<SessionWithActivitiesResource[]>([]);

  useEffect(() => {
    const fetchSessions = async () => {
      const sessions = await user?.getSessions();
      setSessions(sessions ?? []);
    };
    fetchSessions();
  }, [user]);

  return (
    <div className="space-y-8">
      <div>
        <h2 className="font-plus-jakarta mb-6 text-2xl font-bold text-gray-900">
          Security
        </h2>

        <div className="divide-y-2">
          <PasswordSection user={user} />
          <ActiveDevicesSection sessions={sessions} sessionsLoaded={true} />
        </div>
      </div>
    </div>
  );
};
