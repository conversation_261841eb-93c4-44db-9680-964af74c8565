"use client";

import { ChevronRight } from "lucide-react";
import { useState } from "react";

import { cn } from "@/lib/utils";

import { Modal, ModalBody, ModalHeader } from "../ui/modal";
import { ProfileTab } from "./profile-tab";
import { SecurityTab } from "./security-tab";

type TabType = "profile" | "security";

interface UserProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const UserProfileModal = ({
  isOpen,
  onClose,
}: UserProfileModalProps) => {
  const [activeTab, setActiveTab] = useState<TabType>("profile");

  const tabs = [
    { id: "profile" as const, label: "Profile" },
    { id: "security" as const, label: "Security" },
  ];

  return (
    <Modal show={isOpen} onClose={onClose} size="6xl">
      <ModalHeader>
        <span className="font-plus-jakarta text-[32px] font-bold leading-[48px]">
          Account
        </span>
      </ModalHeader>
      <ModalBody className="mt-3 py-0 pl-4">
        <div className="grid h-full min-h-[500px] grid-cols-12 overflow-hidden rounded-lg border border-gray-200">
          {/* Left Panel - Navigation */}
          <div className="col-span-3 overflow-hidden border-r border-gray-200">
            <nav className="space-y-1 overflow-hidden">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={cn(
                    "font-poppins flex w-full items-center justify-between px-4 py-3 text-left text-sm font-medium transition-colors",
                    activeTab === tab.id
                      ? "bg-gray-200"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900",
                  )}
                >
                  <span>{tab.label}</span>
                  <ChevronRight
                    size={16}
                    className={cn("hidden", activeTab === tab.id && "block")}
                  />
                </button>
              ))}
            </nav>
          </div>

          {/* Right Panel - Content */}
          <div className="col-span-9 px-8 py-5">
            {activeTab === "profile" && <ProfileTab />}
            {activeTab === "security" && <SecurityTab />}
          </div>
        </div>
      </ModalBody>
    </Modal>
  );
};
