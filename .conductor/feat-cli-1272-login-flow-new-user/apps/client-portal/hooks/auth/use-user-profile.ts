import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

import api from "@/lib/apis";
import type { UpdateProfilePayload } from "@/lib/apis/users/types";
import { useAuthentication } from "@/stores/authentication";

export const useUserProfile = () => {
  const { user } = useAuthentication();

  return useQuery({
    queryKey: ["user-profiles"],
    queryFn: () =>
      api.users.getUserProfiles(user?.userId ?? "", {
        filter: {
          isActive: true,
        },
      }),
    enabled: !!user?.userId,
  });
};

export const useUpdateUserProfile = () => {
  const { user } = useAuthentication();

  return useMutation({
    mutationFn: (payload: { profileId: string }) =>
      api.users.updateUserCurrentProfile(user?.userId ?? "", payload),
    onSuccess: async () => {
      window.location.href = "/studies";
    },
    onError: (err) => {
      toast.error(err.message);
    },
  });
};

export const useUpdateProfile = () => {
  const { user, setAuthenticated } = useAuthentication();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: Omit<UpdateProfilePayload, "id">) =>
      api.users.updateProfile({
        id: user?.userId ?? "",
        ...payload,
      }),
    onSuccess: async (data) => {
      // Update authentication store with new user data
      if (user) {
        setAuthenticated({
          ...user,
          firstName: data.firstName || user.firstName,
          lastName: data.lastName || user.lastName,
          phone: data.phone || user.phone,
        });
      }

      // Invalidate user-related queries
      await queryClient.invalidateQueries({
        queryKey: ["user-profiles"],
      });

      toast.success("Profile updated successfully!");
    },
    onError: (err: any) => {
      toast.error(err.message || "Failed to update profile");
    },
  });
};
