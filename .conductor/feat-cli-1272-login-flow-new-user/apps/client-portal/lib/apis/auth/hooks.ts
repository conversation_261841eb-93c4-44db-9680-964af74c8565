import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import { auth } from ".";
import type {
  ForgotPasswordRequestPayload,
  ForgotPasswordResendCodePayload,
  ForgotPasswordResetPayload,
  ForgotPasswordVerifyCodePayload,
  ForgotPasswordVerifyCodeResponse,
} from "./types";

/**
 * Hook for requesting a password reset code
 */
export function useForgotPasswordRequest() {
  return useMutation({
    mutationFn: (params: ForgotPasswordRequestPayload) =>
      auth.forgotPasswordRequest(params),
    onSuccess: () => {
      toast.success("Verification code sent to your email");
    },
  });
}

/**
 * Hook for verifying a password reset code
 */
export function useForgotPasswordVerifyCode() {
  return useMutation<
    ForgotPasswordVerifyCodeResponse,
    Error,
    ForgotPasswordVerifyCodePayload
  >({
    mutationFn: (params: ForgotPasswordVerifyCodePayload) =>
      auth.forgotPasswordVerifyCode(params),
    onSuccess: (data) => {
      if (data.isValid) {
        toast.success("Code verified successfully");
      }
    },
  });
}

/**
 * Hook for resetting password with verification code
 */
export function useForgotPasswordReset() {
  return useMutation({
    mutationFn: (params: ForgotPasswordResetPayload) =>
      auth.forgotPasswordReset(params),
    onSuccess: () => {
      toast.success("Password reset successful");
    },
  });
}

/**
 * Hook for resending password reset code
 */
export function useForgotPasswordResendCode() {
  return useMutation({
    mutationFn: (params: ForgotPasswordResendCodePayload) =>
      auth.forgotPasswordResendCode(params),
    onSuccess: () => {
      toast.success("New verification code sent");
    },
  });
}
