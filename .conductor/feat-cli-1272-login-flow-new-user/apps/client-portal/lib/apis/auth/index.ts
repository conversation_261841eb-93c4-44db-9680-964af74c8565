import <PERSON><PERSON><PERSON> from "../base";
import type {
  AuthenticatedUser,
  ForgotPasswordRequestPayload,
  ForgotPasswordResendCodePayload,
  ForgotPasswordResetPayload,
  ForgotPasswordVerifyCodePayload,
  ForgotPasswordVerifyCodeResponse,
  LoginPayload,
  LoginResponse,
  ResetPasswordPayload,
  UpdatePasswordPayload,
} from "./types";

class AuthApi extends BaseApi {
  public constructor() {
    super("/auth", true);
  }

  public async login(params: LoginPayload): Promise<LoginResponse> {
    return this.http.post<LoginResponse>("/login", params);
  }

  public async logout(): Promise<void> {
    return this.http.post<void>("/logout");
  }

  public async authenticated(): Promise<AuthenticatedUser> {
    return this.http.get<AuthenticatedUser>("/authenticated");
  }

  public async requestResetPassword(params: { email: string }): Promise<void> {
    return this.http.post<void>("/request-password-reset", params);
  }

  public async resetPassword(params: ResetPasswordPayload): Promise<void> {
    return this.http.post<void>("/reset-password", params);
  }

  public async updatePassword(params: UpdatePasswordPayload): Promise<void> {
    return this.http.post<void>("/update-password", params);
  }

  public async forgotPasswordRequest(
    params: ForgotPasswordRequestPayload,
  ): Promise<void> {
    return this.http.post<void>("/forgot-password/request", params);
  }

  public async forgotPasswordVerifyCode(
    params: ForgotPasswordVerifyCodePayload,
  ): Promise<ForgotPasswordVerifyCodeResponse> {
    return this.http.post<ForgotPasswordVerifyCodeResponse>(
      "/forgot-password/verify-code",
      params,
    );
  }

  public async forgotPasswordReset(
    params: ForgotPasswordResetPayload,
  ): Promise<void> {
    return this.http.post<void>("/forgot-password/reset", params);
  }

  public async forgotPasswordResendCode(
    params: ForgotPasswordResendCodePayload,
  ): Promise<void> {
    return this.http.post<void>("/forgot-password/resend-code", params);
  }
}

export { AuthApi };
export const auth = new AuthApi();
