import type { Site } from "../site/types";

export type LoginPayload = {
  username: string;
  password: string;
};

export type ResetPasswordPayload = {
  token?: string;
  newPassword: string;
};

export type UpdatePasswordPayload = {
  currentPassword: string;
  newPassword: string;
};

export type ForgotPasswordRequestPayload = {
  email: string;
};

export type ForgotPasswordVerifyCodePayload = {
  code: string;
  email: string;
};

export type ForgotPasswordVerifyCodeResponse = {
  success: boolean;
  message: string;
  isValid: boolean;
  protectionCode: string;
};

export type ForgotPasswordResetPayload = {
  email: string;
  newPassword: string;
  protectionCode: string;
  signOutOfOtherSessions?: boolean;
};

export type ForgotPasswordResendCodePayload = {
  email: string;
};

export type UserType = "family" | "site" | "cro" | "aro" | "clincove";
export type UserStatuses =
  | "invited"
  | "pending_setup"
  | "active"
  | "password_reset_required"
  | "disabled"
  | "locked";

export type UserRole = {
  id: string;
  name: string;
};

export type PermissionSubject = {
  id: string;
  name: string;
  description: string | null;
};

export type UserPermission = {
  id: string;
  action: string;
  description: string;
  permissionSubject: PermissionSubject;
};

export type UserAddress = {
  addressLine: string;
  city: string;
  country?: {
    id: string;
    name: string;
  };
  stateProvince?: {
    id: string;
    name: string;
  };
  zipPostalCode: string;
};

export type UserGroup = {
  id: string;
  groupInstitutionType: string;
  name: string;
  phone: string;
  siteId: string;
  site?: Site;
  type: UserType;
  address: UserAddress;
  entitlements: Entitlement[];
};

export type UserProfile = {
  id: string;
  name: string;
  type: UserType;
  currentGroupId: string;
  currentGroup?: UserGroup;
  shortId?: string;
};

export type UserSettings = {
  profileIcon: {
    color: string;
  };
};

export type User = {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  isActive: boolean;
  userId: string;
  status?: UserStatuses;
  currentProfile: UserProfile;
  roles?: UserRole[];
  permissions?: UserPermission[];
  userSettings?: UserSettings;
};

export type LoginResponse = {
  expiresIn: string;
  user: User;
  permissions: UserPermission[];
};

export type AuthenticatedUser = {
  authenticated: boolean;
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  currentProfile: UserProfile;
  roles?: UserRole[];
  permissions?: UserPermission[];
};

export type EntitlementName =
  | "SMR_CORE"
  | "ISF_CORE"
  | "TMF_CORE"
  | "DOCEXCHANGE_CORE"
  | "EDC_CORE";

export type Entitlement = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  name: EntitlementName;
  displayName: string;
  bundle?: string;
  description?: string;
};
