import type { User } from "../auth/types";
import Base<PERSON><PERSON> from "../base";
import type { StudySitesListResponse } from "../studies/types";
import type { MetadataParams } from "../types";
import type {
  InviteUserListResponse,
  InviteUserPayload,
  UpdateProfilePayload,
  UpdateUserCurrentProfilePayload,
  UpdateUserPayload,
  UpdateUserStatusPayload,
  UserAssignment,
  UserAssignmentListResponse,
  UserListResponse,
  UserProfileListResponse,
} from "./types";

export * from "./types";

class UsersApi extends BaseApi {
  constructor() {
    super("/users", true);
  }

  public async getUserAssignments(id: string) {
    return this.http.get<UserAssignmentListResponse>(`/${id}/assignments`);
  }

  public async updateUserAssignment(
    id: string,
    payload: { assignmentId: string },
  ) {
    return this.http.patch<UserAssignment>(`/${id}/assignment`, payload);
  }

  public async getUserProfile(id: string) {
    return this.http.get<User>(`/${id}`);
  }

  public async updateProfile({ id, ...payload }: UpdateProfilePayload) {
    return this.http.put<User>(`/${id}/profile`, payload);
  }

  public async getUserProfiles(id: string, params: MetadataParams = {}) {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<UserProfileListResponse>(
      `/${id}/profiles?${paramUrl}`,
    );
  }

  public async updateUserCurrentProfile(
    id: string,
    payload: UpdateUserCurrentProfilePayload,
  ) {
    return this.http.patch<void>(`/${id}/profiles`, payload);
  }

  public async groupUsers(params: MetadataParams = {}) {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<UserListResponse>(`/me/group-members?${paramUrl}`);
  }

  public async groupInvites(params: MetadataParams = {}) {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<InviteUserListResponse>(`/me/invites?${paramUrl}`);
  }

  public async deleteUser(userId: string) {
    return this.http.delete<void>(`/${userId}`);
  }

  public async disableUser(userId: string) {
    return this.http.patch<void>(`/${userId}/disable`);
  }

  public async updateUser(userId: string, payload: UpdateUserPayload) {
    return this.http.patch<User>(`/${userId}`, payload);
  }

  public async inviteUser(payload: InviteUserPayload) {
    return this.http.post<void>(`/invite`, payload);
  }

  public async studies(params: MetadataParams = {}) {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<StudySitesListResponse>(`/studies?${paramUrl}`);
  }

  public async removeStudy(userId: string, studyId: string) {
    return this.http.delete<void>(`/${userId}/studies/${studyId}`);
  }

  public async updateStatus(userId: string, payload: UpdateUserStatusPayload) {
    return this.http.patch<void>(`/${userId}/status`, payload);
  }
}

export const users = new UsersApi();
