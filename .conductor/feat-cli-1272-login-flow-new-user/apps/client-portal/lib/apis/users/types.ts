import { User, UserProfile } from "../../models/user";
import type { ListBaseResponse } from "../types";

type Institution = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  name: string;
  phone: string;
  email: string;
  type: string | null;
};

export type UserAssignment = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  name: string;
  description: string | null;
  type: string;
  institutionId: string;
  institution: Institution;
};

export type UpdateProfilePayload = {
  firstName: string;
  lastName: string;
  phone?: string;
  id: string;
};

export type UpdateUserCurrentProfilePayload = {
  profileId: string;
};

export type UserAssignmentListResponse = ListBaseResponse<UserAssignment>;
export type UserProfileListResponse = ListBaseResponse<UserProfile>;

export type UserListResponse = ListBaseResponse<User>;

export type InviteUser = {
  id: string;
  emailAddress: string;
  publicMetadata: {
    email: string;
    phone: string | null;
    region: string;
    roleId: string;
    groupId: string;
    lastName: string;
    firstName: string;
    groupType: string;
  };
  createdAt: string;
  updatedAt: string;
  url: string;
  status: string;
  role: {
    id: string;
    name: string;
    type: string;
  };
};

export type InviteUserListResponse = ListBaseResponse<InviteUser>;

export type UpdateUserPayload = {
  firstName: string;
  lastName: string;
  phone?: string;
  email: string;
  roleId: string;
  studyIds: string[];
};

export type DisableUserPayload = {
  isActive: boolean;
  status: string;
};

export type UpdateUserStatusPayload = {
  status: string;
};

export type InviteUserPayload = {
  firstName: string;
  lastName: string;
  phone?: string;
  email: string;
  roleId: string;
  studyIds: string[];
};
