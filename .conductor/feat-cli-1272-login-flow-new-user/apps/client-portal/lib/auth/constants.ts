/**
 * Authentication constants for the Client Portal
 */

import type { UserType } from "@/lib/apis/auth/types";

// Supported user types for the Client Portal
export const SUPPORTED_USER_TYPES: UserType[] = ["site", "cro"];

// User types that should be redirected to Admin Portal
export const ADMIN_USER_TYPES: UserType[] = ["clincove"];

// User types that are not supported in any portal
export const UNSUPPORTED_USER_TYPES: UserType[] = ["family", "aro"];

// Default redirect paths for each user type
export const USER_TYPE_REDIRECT_PATHS: Record<UserType, string | null> = {
  site: "/sit",
  cro: "/cro",
  family: null,
  aro: null,
  clincove: null, // Will be redirected to admin portal
};

// Authentication query keys for React Query
export const AUTH_QUERY_KEYS = {
  authenticated: ["auth", "authenticated"] as const,
  userProfile: ["auth", "user-profile"] as const,
  userProfiles: ["auth", "user-profiles"] as const,
} as const;

// Authentication error messages
export const AUTH_ERROR_MESSAGES = {
  NETWORK_ERROR: "Network error occurred. Please check your connection.",
  INVALID_TOKEN: "Your session has expired. Please sign in again.",
  UNAUTHORIZED: "You are not authorized to access this application.",
  INVALID_ROLE: "Your role is not supported in this application.",
  USER_INACTIVE: "Your account is inactive. Please contact support.",
  UNKNOWN_ERROR: "An unexpected error occurred. Please try again.",
} as const;

// User type error messages
export const USER_TYPE_ERROR_MESSAGES = {
  INVALID_USER_TYPE: "Invalid user type detected.",
  UNSUPPORTED_USER_TYPE: "Your user type is not supported in this application.",
  USER_TYPE_RESOLUTION_FAILED: "Failed to determine your user type.",
} as const;

// Inactivity timeout (in milliseconds)
export const DEFAULT_INACTIVITY_TIMEOUT = 30 * 60 * 1000; // 30 minutes

// Password reset cooldown duration (in seconds)
export const PASSWORD_RESET_COOLDOWN_SECONDS = 60;
