import { zxcvbn, zxcvbnOptions } from "@zxcvbn-ts/core";
import { dictionary, translations } from "@zxcvbn-ts/language-en";

/**
 * Password strength evaluation result interface
 */
export interface PasswordStrengthResult {
  isStrong: boolean;
  score: number;
  feedback: string[];
}

/**
 * Service for evaluating password strength using zxcvbn
 * Matches the backend implementation exactly
 */
export class PasswordStrengthService {
  constructor() {
    zxcvbnOptions.setOptions({
      translations,
      dictionary,
    });
  }

  /**
   * Evaluates password strength using zxcvbn algorithm
   * @param password - The password to evaluate
   * @returns PasswordStrengthResult with strength information
   */
  public evaluatePasswordStrength(password: string): PasswordStrengthResult {
    const result = zxcvbn(password);

    if (result.score >= 3) {
      return { isStrong: true, score: result.score, feedback: [] };
    } else {
      const feedbackMessages = result.feedback.suggestions.length
        ? result.feedback.suggestions
        : [
            "Password is too weak. Consider using a mix of letters, numbers, and special characters.",
          ];
      return {
        isStrong: false,
        score: result.score,
        feedback: feedbackMessages,
      };
    }
  }

  /**
   * Validates if password meets minimum strength requirements
   * @param password - The password to validate
   * @returns true if password is strong enough (score >= 3), false otherwise
   */
  public isPasswordStrong(password: string): boolean {
    const result = this.evaluatePasswordStrength(password);
    return result.isStrong;
  }

  /**
   * Gets feedback messages for password improvement
   * @param password - The password to get feedback for
   * @returns Array of feedback messages for improving the password
   */
  public getPasswordFeedback(password: string): string[] {
    const result = this.evaluatePasswordStrength(password);
    return result.feedback;
  }
}

export const passwordStrengthService = new PasswordStrengthService();
