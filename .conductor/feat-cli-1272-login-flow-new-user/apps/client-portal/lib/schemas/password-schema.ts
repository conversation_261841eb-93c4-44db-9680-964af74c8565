import { z } from "zod";

import { passwordStrengthService } from "../password-strength";

/**
 * Strong password validation schema using zxcvbn
 * Requires minimum score of 3 and provides helpful feedback
 */
export const strongPasswordSchema = z
  .string()
  .min(1, "Password is required")
  .refine(
    (password) => passwordStrengthService.isPasswordStrong(password),
    (password) => {
      const feedback = passwordStrengthService.getPasswordFeedback(password);
      return {
        message:
          feedback.join(" ") ||
          "Password is too weak. Please choose a stronger password.",
      };
    },
  );

/**
 * Password confirmation schema that ensures passwords match
 */
export const passwordConfirmationSchema = z
  .string()
  .min(1, "Please confirm your password");

/**
 * Complete password form schema with password and confirmation
 */
export const passwordFormSchema = z
  .object({
    password: strongPasswordSchema,
    confirmPassword: passwordConfirmationSchema,
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

/**
 * Type definitions for the password form schema
 */
export type PasswordFormData = z.infer<typeof passwordFormSchema>;
export type StrongPassword = z.infer<typeof strongPasswordSchema>;

/**
 * Helper function to validate a password and get detailed feedback
 * @param password - The password to validate
 * @returns Object with validation result and feedback messages
 */
export function validatePasswordWithFeedback(password: string): {
  isValid: boolean;
  score: number;
  feedback: string[];
} {
  const result = passwordStrengthService.evaluatePasswordStrength(password);
  return {
    isValid: result.isStrong,
    score: result.score,
    feedback: result.feedback,
  };
}
